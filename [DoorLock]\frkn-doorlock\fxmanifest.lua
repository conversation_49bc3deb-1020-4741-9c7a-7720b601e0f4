fx_version 'cerulean'
game 'gta5'

dependencies {
	'core'
}

escrow_ignore {
	'client/*.lua',
	'server/*.lua',
	'shared/*.lua',
	'doors/*.lua',
	'locales/*.lua',
}
shared_scripts {
	'shared/locale.lua',
	'locales/en.lua',
	'locales/*.lua',
	'shared/cores.lua',
    'shared/config.lua',
	'doors/*.lua',
}
client_scripts {
	'client/*.lua'
}
server_scripts {
	'server/*.lua'
}
ui_page 'html/index.html'
files {
	'html/index.html',
	'html/style.css',
	'html/script.js',
	'html/*png',
	'html/*jpg',
	'html/*webp',
	'html/sounds/*.ogg',
	'assets/**/*.png',
}
lua54 'yes'
dependency '/assetpacks'

game "gta5"
this_is_a_map 'yes'

dependency '/assetpacks'
dependency '/assetpacks'