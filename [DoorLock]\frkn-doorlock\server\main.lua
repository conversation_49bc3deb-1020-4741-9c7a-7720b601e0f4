-- Functions

local function showWarning(msg)
	print(('^3%s: %s^0'):format(Lang:t("general.warning"), msg))
end

local function removeItem(Player, item)
	if Config.Consumables[item.name] then
		RemoveItem(item.name, item.amount >= Config.Consumables[item.name] and Config.Consumables[item.name] or 1)
	end
end

local function checkAndRemoveItem(Player, item, shouldRemove)
	if not item then return false end
	if shouldRemove then
		removeItem(Player, item)
	end
	return true
end

local function checkItems(Player, items, needsAll, shouldRemove)
	if needsAll == nil then needsAll = true end
	local isTable = type(items) == 'table'
	local isArray = isTable and table.type(items) == 'array' or false
	local totalItems = 0
	local count = 0
	if isTable then for _ in pairs(items) do totalItems += 1 end else totalItems = #items end
	local kvIndex
	if isArray then kvIndex = 2 else kvIndex = 1 end
	if isTable then
		for k, v in pairs(items) do
			local itemKV = {k, v}
			local item = GetItemByName(Player,itemKV[kvIndex])
			if needsAll then
				if checkAndRemoveItem(Player, item, false) then
					count += 1
				end
			else
				if checkAndRemoveItem(Player, item, shouldRemove) then
					return true
				end
			end
		end
		if count == totalItems then
			for k, v in pairs(items) do
				local itemKV = {k, v}
				local item = GetItemByName(Player,itemKV[kvIndex])
				checkAndRemoveItem(Player, item, shouldRemove)
			end
			return true
		end
	else -- Single item as string
		local item = GetItemByName(Player,items)
		return checkAndRemoveItem(Player, item, shouldRemove)
	end
	return false
end

local function isAuthorized(Player, door, usedLockpick, id)
	if door.allAuthorized then return true end
	if CoreName == "qb-core" or CoreName == "qbx_core" then
		if Config.AdminAccess and Core.Functions.HasPermission(Player.PlayerData.source, Config.AdminPermission) then
			if Config.Warnings then
				showWarning(Lang:t("general.warn_admin_privilege_used", {player = Player.PlayerData.name, license = Player.PlayerData.license}))
			end
			return true
		end
	elseif CoreName == "es_extended" then
		local pData = GetPlayer(Player.source)
		local pGroup = pData.getGroup()
		if Config.AdminAccess and pGroup == Config.CommandPermission then
			return true
		end
	end
	if (door.pickable or door.lockpick) and usedLockpick then return true end
	local acJob = GetPlayerJob(id)
	local acGrade = GetPlayerGrade(id)
	if door.authorizedJobs then
		if door.authorizedJobs[acJob.name] and acGrade >= door.authorizedJobs[acJob.name] then
			return true
		elseif type(door.authorizedJobs[1]) == 'string' then
			for _, job in pairs(door.authorizedJobs) do -- Support for old format
				if job == acJob.name then return true end
			end
		end
	-- else
	-- 	return true
	end

	if CoreName == "qb-core" or CoreName == "qbx_core" then
		if door.authorizedGangs then
			if door.authorizedGangs[Player.PlayerData.gang.name] and acGrade >= door.authorizedGangs[Player.PlayerData.gang.name] then
				return true
			elseif type(door.authorizedGangs[1]) == 'string' then
				for _, gang in pairs(door.authorizedGangs) do -- Support for old format
					if gang == Player.PlayerData.gang.name then return true end
				end
			end
		end
	else
		if door.authorizedGangs then
			if door.authorizedGangs[acJob.name] and acGrade >= door.authorizedGangs[acJob.name] then
				return true
			elseif type(door.authorizedGangs[1]) == 'string' then
				for _, gang in pairs(door.authorizedGangs) do -- Support for old format
					if gang == acJob.name then return true end
				end
			end
		end
	end

	if door.authorizedCitizenIDs then
		if door.authorizedCitizenIDs[GetIdentifier(Player.source)] then
			return true
		elseif type(door.authorizedCitizenIDs[1]) == 'string' then
			for _, id in pairs(door.authorizedCitizenIDs) do -- Support for old format
				if id == GetIdentifier(Player.source) then return true end
			end
		end
	end
	if door.items then return checkItems(Player, door.items, door.needsAllItems, true) end

	return false
end

local function SaveDoorStates()
    SaveResourceFile(GetCurrentResourceName(), "./saves/doorstates.json", json.encode(Config.DoorStates), -1)
end

local function LoadDoorStates()
	local DoorStates = LoadResourceFile(GetCurrentResourceName(), "./saves/doorstates.json")
	if DoorStates then
		DoorStates = json.decode(DoorStates)
		if not next(DoorStates) then return end

		for key,isLocked in pairs(DoorStates) do
			if Config.DoorList[key] ~= nil then
				Config.DoorList[key].locked = isLocked
			end
		end
		Config.DoorStates = DoorStates
	end
end

-- Callbacks
CreateCallback('frkn-doorlock:server:setupDoors', function(_, cb)
	cb(Config.DoorList)
end)

CreateCallback('frkn-doorlock:server:checkItems', function(source, cb, items, needsAll)
	local Player = GetPlayer(source)
	cb(checkItems(Player, items, needsAll, false))
end)

-- Events
RegisterNetEvent('frkn-doorlock:server:updateState', function(doorID, locked, src, usedLockpick, unlockAnyway, enableSounds, enableAnimation, sentSource)
	local playerId = sentSource or source
	local Player = GetPlayer(playerId)
	local name = nil
	if CoreName == "qb-core" or CoreName == "qbx_core" then
		name = Player.PlayerData.name
    elseif CoreName == "es_extended" then
		name = GetPlayerName(playerId)
	end
	local license = nil
	if CoreName == "qb-core" or CoreName == "qbx_core" then
		license = Player.PlayerData.license
    elseif CoreName == "es_extended" then
		license = Player.identifier
	end
	if not Player then return end
	if type(doorID) ~= 'number' and type(doorID) ~= 'string' then
		if Config.Warnings then
			showWarning((Lang:t("general.warn_wrong_doorid_type", {player = name, license = license, doorID = doorID})))
		end
		return
	end

	if type(locked) ~= 'boolean' then
		if Config.Warnings then
			showWarning((Lang:t("general.warn_wrong_state", {player = name, license = license, state = locked})))
		end
		return
	end

	if not Config.DoorList[doorID] then
		if Config.Warnings then
			showWarning(Lang:t("general.warn_wrong_doorid", {player = ame, license = license, doorID = doorID}))
		end
		return
	end

	if not unlockAnyway and not isAuthorized(Player, Config.DoorList[doorID], usedLockpick, playerId) then
		if Config.Warnings then
			showWarning(Lang:t("general.warn_no_authorisation", {player = name, license = license, doorID = doorID}))
		end
		return
	end

	Config.DoorList[doorID].locked = locked
	if Config.DoorStates[doorID] == nil then Config.DoorStates[doorID] = locked elseif Config.DoorStates[doorID] ~= locked then Config.DoorStates[doorID] = nil end
	TriggerClientEvent('frkn-doorlock:client:setState', -1, playerId, doorID, locked, src or false, enableSounds, enableAnimation)

	if not Config.DoorList[doorID].autoLock then return end
	SetTimeout(Config.DoorList[doorID].autoLock, function()
		if Config.DoorList[doorID].locked then return end
		Config.DoorList[doorID].locked = true
		if Config.DoorStates[doorID] == nil then Config.DoorStates[doorID] = locked elseif Config.DoorStates[doorID] ~= locked then Config.DoorStates[doorID] = nil end
		TriggerClientEvent('frkn-doorlock:client:setState', -1, playerId, doorID, true, src or false, enableSounds, enableAnimation)
	end)
end)

RegisterNetEvent('frkn-doorlock:server:saveNewDoor', function(data, doubleDoor)
	local src = source
	if CoreName == "qb-core" or CoreName == "qbx_core" then
		if not Core.Functions.HasPermission(src, Config.CommandPermission) and not IsPlayerAceAllowed(src, 'command') then
			if Config.Warnings then
				showWarning(Lang:t("general.warn_no_permission_newdoor", {player = GetPlayerName(src), license = Core.Functions.GetIdentifier(src, 'license'), source = src}))
			end
			return
		end
	elseif CoreName == "es_extended" then
		local pData = GetPlayer(src)
		local pGroup = pData.getGroup()
		if not pGroup == Config.CommandPermission then
			if Config.Warnings then
				showWarning(Lang:t("general.warn_no_permission_newdoor", {player = GetPlayerName(src), license = Core.Functions.GetIdentifier(src, 'license'), source = src}))
			end
			return
		end
	end
	local Player = GetPlayer(src)
	if not Player then return end
	local configData = {}
	local jobs, gangs, cids, items, doorType, identifier
	if data.job then configData.authorizedJobs = { [data.job] = 0 } jobs = "['"..data.job.."'] = 0" end
	if data.gang then configData.authorizedGangs = { [data.gang] = 0 } gangs = "['"..data.gang.."'] = 0" end
	if data.cid then configData.authorizedCitizenIDs = { [data.cid] = true } cids = "['"..data.cid.."'] = true" end
	if data.item then configData.items = { [data.item] = 1 } items = "['"..data.item.."'] = 1" end
	configData.locked = data.locked
	configData.pickable = data.pickable
	configData.cantUnlock = data.cantunlock
	configData.svgDistance = data.svgDistance
	configData.interactDistance = data.interactDistance
	configData.doorType = data.doortype
	configData.positionData = data.positionData
	configData.owner = GetIdentifier(src)
	if data.allAuthorized then
		configData.allAuthorized = true
	end
	if data.passCode == '' then 
		configData.passCode = nil 
	else 
		configData.passCode = "'"..data.passCode.."'"
	end
	configData.doorRate = 1.0
	doorType = "'"..data.doortype.."'"
	identifier = data.configfile..'-'..data.dooridentifier
	if doubleDoor then
		configData.doors = {
			{objName = data.model[1], objYaw = data.heading[1], objCoords = data.coords[1]},
			{objName = data.model[2], objYaw = data.heading[2], objCoords = data.coords[2]}
		}
	else
		configData.objName = data.model
		configData.objYaw = data.heading
		configData.objCoords = data.coords
		configData.fixText = data.fixText
	end

	local path = GetResourcePath(GetCurrentResourceName())
	if data.configfile then
		local tempfile, err = io.open(path:gsub('//', '/')..'/doors/'..string.gsub(data.configfile, ".lua", "")..'.lua', 'a+')
		if tempfile then
			tempfile:close()
			path = path:gsub('//', '/')..'/doors/'..string.gsub(data.configfile, ".lua", "")..'.lua'
		else
			return error(err)
		end
	else
		path = path:gsub('//', '/')..'/config.lua'
	end
	local name = nil
	if CoreName == "qb-core" or CoreName == "qbx_core" then
		name = Player.PlayerData.name
    elseif CoreName == "es_extended" then
		name = GetPlayerName(src)
	end
	local file = io.open(path, 'a+')
	local label = "\n\n-- "..data.dooridentifier.." ".. Lang:t("general.created_by") .." "..name.."\nConfig.DoorList['"..identifier.."'] = {"
	file:write(label)
	for k, v in pairs(configData) do
		if k == 'authorizedJobs' or k == 'authorizedGangs' or k == 'authorizedCitizenIDs' or k == 'items' then
			local auth = jobs
			if k == 'authorizedGangs' then
				auth = gangs
			elseif k == 'authorizedCitizenIDs' then
				auth = cids
			elseif k == 'items' then
				auth = items
			end
			local str = ("\n    %s = { %s },"):format(k, auth)
			file:write(str)
		elseif k == 'doors' then
			local doors = {}
			for i = 1, 2 do
				doors[i] = ("    {objName = %s, objYaw = %s, objCoords = vector3(%f, %f, %f)}"):format(configData.doors[i].objName, configData.doors[i].objYaw, configData.doors[i].objCoords.x, configData.doors[i].objCoords.y, configData.doors[i].objCoords.z)
			end
			local str = ("\n    %s = {\n    %s,\n    %s\n    },"):format(k, doors[1], doors[2])
			file:write(str)
		elseif k == 'doorType' then
			local str = ("\n    %s = %s,"):format(k, doorType)
			file:write(str)
		elseif k == 'positionData' then
			local heading = v[1] and v[1].heading or 0.0 
			local posDataStr = ("\n    %s = {\n        position = vector3(%f, %f, %f),\n        rotation = vector3(%f, %f, %f),\n        heading = %f,\n        handle = %d\n    },"):format(
				k, 
				v.position.x, v.position.y, v.position.z,
				v.rotation.x, v.rotation.y, v.rotation.z,
				heading, 
				v.handle
			)
			file:write(posDataStr)
		elseif k == 'owner' then
			local str = ("\n    %s = '%s',"):format(k, v)
			file:write(str)
		else
			local str = ("\n    %s = %s,"):format(k, v)
			file:write(str)
		end
	end
	file:write("\n}")
	file:close()
	Config.DoorList[identifier] = configData
	TriggerClientEvent('frkn-doorlock:client:newDoorAdded', -1, configData, identifier, src)
end)


AddEventHandler('onResourceStart', function(resource)
    if GetCurrentResourceName() == resource and Config.PersistentDoorStates then
		CreateThread(function()
			LoadDoorStates()
			Wait(1000)
			while true do
				Wait(Config.PersistentSaveInternal)
				SaveDoorStates()
			end
		end)
    end
end)

AddEventHandler('onResourceStop', function(resource)
    if GetCurrentResourceName() == resource and Config.PersistentDoorStates then
		SaveDoorStates()
    end
end)

RegisterNetEvent('txAdmin:events:scheduledRestart', function(eventData)
    if eventData.secondsRemaining == 60 then
        CreateThread(function()
            Wait(45000)
			SaveDoorStates()
        end)
	else
		SaveDoorStates()
    end
end)


RegisterNetEvent('frkn-doorlock:server:removeLockpick', function(type)
	local Player = GetPlayer(source)
	if not Player then return end
	if type == "advancedlockpick" or type == "lockpick" then
		RemoveItem(type, 1)
	end
end)

-- Commands
Citizen.CreateThread(function()
	while CoreReady == false do Citizen.Wait(0) end
	if CoreName == "qb-core" or CoreName == "qbx_core" then
		Core.Commands.Add('createsingledoor', Lang:t("general.newdoor_command_description"), {}, false, function(source)
			TriggerClientEvent('frkn-doorlock:client:createNewSingleDoor', source)
		end, Config.CommandPermission)
		Core.Commands.Add('createdoubledoor', Lang:t("general.newdoor_command_description"), {}, false, function(source)
			TriggerClientEvent('frkn-doorlock:client:createNewDoubleDoor', source)
		end, Config.CommandPermission)
		Core.Commands.Add('doordebug', Lang:t("general.doordebug_command_description"), {}, false, function(source)
			TriggerClientEvent('frkn-doorlock:client:ToggleDoorDebug', source)
		end, Config.CommandPermission)
		Core.Commands.Add('doorsList', Lang:t("general.doorlist_command_description"), {}, false, function(source)
			TriggerClientEvent('frkn-doorlock:client:openDoorList', source)
		end, Config.CommandPermission)
	elseif CoreName == "es_extended" then
		Core.RegisterCommand('createsingledoor', Config.CommandPermission, function(xPlayer, args, showError)
			xPlayer.triggerEvent('frkn-doorlock:client:createNewSingleDoor')
		end, false, {help = Lang:t("general.newdoor_command_description")})
		Core.RegisterCommand('createdoubledoor', Config.CommandPermission, function(xPlayer, args, showError)
			xPlayer.triggerEvent('frkn-doorlock:client:createNewDoubleDoor')
		end, false, {help = Lang:t("general.newdoor_command_description")})
		Core.RegisterCommand('doordebug', Config.CommandPermission, function(xPlayer, args, showError)
			xPlayer.triggerEvent('frkn-doorlock:client:ToggleDoorDebug')
		end, false, {help = Lang:t("general.doordebug_command_description")})
		Core.RegisterCommand('doorsList', Config.CommandPermission, function(xPlayer, args, showError)
			xPlayer.triggerEvent('frkn-doorlock:client:openDoorList')
		end, false, {help = Lang:t("general.doorlist_command_description")})
	end
end)



function getDiscordImg(src)
    local src = source
    -- Identifier
    local myIdentifier = nil
    local identifiers = GetPlayerIdentifiers(src)
    for _, identifier in pairs(identifiers) do
        if string.find(identifier, "license:") then
            myIdentifier = identifier
        end
    end
    while myIdentifier == nil do Citizen.Wait(0) end
    -- Discord
    local myDiscord = nil
    for _, identifier in pairs(identifiers) do
        if string.find(identifier, "discord:") then
            myDiscord = string.gsub(identifier, "discord:", "")
        end
    end
    local discordProfileAvatar = "./defaultpp.webp"
    while myDiscord == nil do Citizen.Wait(5000) if myDiscord == nil then break end end
    local endpoint = ("users/%s"):format(myDiscord)
    local member = DiscordRequest("GET", endpoint, {})
    local memberData = json.decode(member.data)
    if memberData ~= nil then
        discordProfileAvatar = "https://cdn.discordapp.com/avatars/" .. myDiscord .. "/" .. memberData.avatar
    end
    return discordProfileAvatar
end 

function DiscordRequest(method, endpoint, jsondata, reason)
    local FormattedToken = "Bot " .. Config.BokToken
    local data = nil
    PerformHttpRequest("https://discord.com/api/"..endpoint, function(errorCode, resultData, resultHeaders) data = {data=resultData, code=errorCode, headers=resultHeaders}
    end, method, #jsondata > 0 and jsondata or "", {["Content-Type"] = "application/json", ["Authorization"] = FormattedToken, ['X-Audit-Log-Reason'] = reason})
    while data == nil do Citizen.Wait(0) end
    return data
end


CreateCallback('frkn-doorlock:getPlayer', function(source, cb)
    local src = source
    local discordProfile = getDiscordImg(src)
	player = {
		name = GetPlayerName(src),
		source = src,
		discord = discordProfile
	}
    cb(player)
end)


CreateCallback('frkn-doorlock:server:playerIdentifier',function (source, cb)
    local src = source
	cb(GetIdentifier(source))
end)


